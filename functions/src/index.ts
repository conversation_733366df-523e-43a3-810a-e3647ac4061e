import * as admin from 'firebase-admin';
import { setUserRole, createUser, updateUserStatus } from './auth';
import { onUserDocumentCreated, onPaymentStatusChange } from './triggers';
import { calculateProjectFinancials, updatePaymentStatus, updateProjectCostOnChangeRequest, deletePayment } from './financial';
import { generateInvoice } from './invoice/generateInvoice';
import { getInvoices, updateInvoiceStatus, generateConsolidatedInvoice } from './invoice/invoiceManagement';
import { downloadInvoice } from './invoice/downloadInvoice';

// Initialize Firebase Admin SDK
admin.initializeApp();

// Export only the required functions
export { 
  setUserRole, 
  createUser, 
  updateUserStatus, 
  onUserDocumentCreated,
  onPaymentStatusChange,
  calculateProjectFinancials,
  updatePaymentStatus,
  updateProjectCostOnChangeRequest,
  deletePayment,
  // Invoice functions - manual only
  generateInvoice,
  getInvoices,
  updateInvoiceStatus,
  generateConsolidatedInvoice,
  downloadInvoice
}; 