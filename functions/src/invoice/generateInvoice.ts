import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface GenerateInvoiceRequest {
  type: 'individual' | 'consolidated';
  projectId: string;
  clientId: string;
  paymentIds: string[];
  description?: string;
  notes?: string;
  dueDate?: Date;
}

export const generateInvoice = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GenerateInvoiceRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    // Validate inputs
    if (!data.projectId || !data.clientId || !data.paymentIds?.length) {
      throw new Error('Missing required fields');
    }

    // Get project details
    const projectDoc = await db.collection('projects').doc(data.projectId).get();

    if (!projectDoc.exists) {
      throw new Error('Project not found');
    }

    const project = projectDoc.data();

    // Get payment details
    const paymentDocs = await Promise.all(
      data.paymentIds.map(id => db.collection('payments').doc(id).get())
    );

    const payments = paymentDocs
      .filter(doc => doc.exists)
      .map(doc => ({ id: doc.id, ...doc.data() })) as any[];

    if (payments.length === 0) {
      throw new Error('No valid payments found');
    }

    // Validate all payments are paid
    const unpaidPayments = payments.filter((p: any) => p.status !== 'paid');
    if (unpaidPayments.length > 0) {
      throw new Error('All payments must be paid to generate invoice');
    }

    // Calculate invoice totals
    const amount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
    const totalAmount = amount;

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber(db);

    // Create invoice document
    const invoiceData: any = {
      invoiceNumber,
      type: data.type,
      projectId: data.projectId,
      clientId: data.clientId,
      
      // Financial details
      amount,
      currency: (payments[0] as any).currency || 'INR',
      totalAmount,
      
      // Invoice metadata
      status: 'sent' as const,
      issueDate: Timestamp.now(),
      dueDate: data.dueDate ? Timestamp.fromDate(new Date(data.dueDate)) : Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
      
      // Content
      description: data.description || generateDefaultDescription(data.type, project?.name, payments.length),
      
      // Related entities
      paymentIds: data.paymentIds,
      linkedTicketIds: payments.flatMap((p: any) => p.linkedTicketId ? [p.linkedTicketId] : []),
      
      // Generation metadata
      generatedBy: userId,
      generatedAt: Timestamp.now(),
      
      // Tracking
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    // Only add notes if it's not undefined/null to avoid Firestore validation errors
    if (data.notes !== undefined && data.notes !== null && data.notes !== '') {
      invoiceData.notes = data.notes;
    }

    // Save invoice
    const invoiceRef = db.collection('invoices').doc();
    await invoiceRef.set(invoiceData);

    // Update payments with invoice reference
    const batch = db.batch();
    data.paymentIds.forEach(paymentId => {
      const paymentRef = db.collection('payments').doc(paymentId);
      batch.update(paymentRef, {
        invoiceId: invoiceRef.id,
        invoicedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
    await batch.commit();

    logger.info(`Invoice ${invoiceNumber} generated for ${data.paymentIds.length} payments`);

    return {
      success: true,
      invoiceId: invoiceRef.id,
      invoiceNumber,
      totalAmount,
      paymentCount: data.paymentIds.length
    };

  } catch (error) {
    logger.error('Error generating invoice:', error);
    throw error;
  }
});

async function generateInvoiceNumber(db: FirebaseFirestore.Firestore): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `INV-${year}-`;
  
  // Get the latest invoice number for this year
  const invoicesSnapshot = await db
    .collection('invoices')
    .where('invoiceNumber', '>=', prefix)
    .where('invoiceNumber', '<', `INV-${year + 1}-`)
    .orderBy('invoiceNumber', 'desc')
    .limit(1)
    .get();

  let nextNumber = 1;
  if (!invoicesSnapshot.empty) {
    const lastInvoiceNumber = invoicesSnapshot.docs[0].data().invoiceNumber;
    const lastNumber = parseInt(lastInvoiceNumber.split('-')[2]) || 0;
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
}

function generateDefaultDescription(type: string, projectName: string, paymentCount: number): string {
  if (type === 'individual') {
    return `Invoice for payment - ${projectName}`;
  } else {
    return `Consolidated invoice for ${paymentCount} payments - ${projectName}`;
  }
}
