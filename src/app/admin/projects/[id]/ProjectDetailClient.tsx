'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useProjectFinancials } from '@/hooks/useProjectFinancials';
import { useProjectManagement, ProjectManagementProvider } from '@/hooks/useProjectManagement';
import { useActivityLogger } from '@/hooks/useActivityLogger';
import { UserManagementProvider } from '@/hooks/useUserManagement';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  ArrowLeft, 
  RefreshCw, 
  DollarSign, 
  TrendingUp, 
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Calculator,
  Ticket as TicketIcon,
  Plus,
  Search,
  User,
  AlertTriangle,
  Trash2,
  FileText,
  Download,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import Link from 'next/link';
import { Timestamp } from 'firebase/firestore';
import { TicketType, TicketPriority, TicketStatus, Ticket, PaymentType, Payment, PaymentStatus, Project } from '@/types/project';
import { TicketCreationForm } from '@/components/admin/forms/TicketCreationForm';
import { TicketEditForm } from '@/components/admin/forms/TicketEditForm';
import { PaymentCreationForm } from '@/components/admin/forms/PaymentCreationForm';
import { PaymentEditForm } from '@/components/admin/forms/PaymentEditForm';
import { DeleteConfirmationDialog } from '@/components/admin/DeleteConfirmationDialog';
import { InlineProjectEdit } from '@/components/admin/forms/InlineProjectEdit';
import { ProjectClientsManager } from '@/components/admin/ProjectClientsManager';


interface ProjectDetailClientProps {
  projectId: string;
}

function ProjectDetailContent({ projectId }: ProjectDetailClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { logActivity } = useActivityLogger();
  const { user } = useAuth();
  
  const { 
    financialSummary, 
    projectPayments, 
    loading, 
    calculating,
    updatePaymentStatus,
    refreshFinancials
  } = useProjectFinancials(projectId);

  const { 
    projects, 
    tickets,
    payments,
    ticketsLoading,
    paymentsLoading,
    loadProjects, 
    loadTickets,
    loadPayments,
    createTicket,
    updateTicket,
    deleteTicket,
    createPayment,
    updatePayment,
    deletePayment,
    checkTicketRelationships,
    updateProject
  } = useProjectManagement();

  const project = projects.find(p => p.id === projectId);

  const [activeTab, setActiveTab] = useState('overview');
  // Add edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Ticket state
  const [ticketSearchTerm, setTicketSearchTerm] = useState('');
  const [ticketStatusFilter, setTicketStatusFilter] = useState<string>('all');
  const [ticketPriorityFilter, setTicketPriorityFilter] = useState<string>('all');
  const [createTicketDialogOpen, setCreateTicketDialogOpen] = useState(false);
  const [editTicketDialogOpen, setEditTicketDialogOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [ticketCreating, setTicketCreating] = useState(false);
  const [ticketUpdating, setTicketUpdating] = useState<string | null>(null);

  // Payment state
  const [paymentSearchTerm, setPaymentSearchTerm] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all');
  const [createPaymentDialogOpen, setCreatePaymentDialogOpen] = useState(false);
  const [editPaymentDialogOpen, setEditPaymentDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [paymentCreating, setPaymentCreating] = useState(false);
  const [paymentUpdating, setPaymentUpdating] = useState<string | null>(null);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [bulkInvoiceGenerating, setBulkInvoiceGenerating] = useState(false);

  // Check if edit mode was requested via URL parameter
  useEffect(() => {
    const mode = searchParams.get('mode');
    if (mode === 'edit') {
      setIsEditMode(true);
      // Clean up URL parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('mode');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  // Load initial data
  useEffect(() => {
    if (projects.length === 0) {
      loadProjects(true);
    }
    loadTickets(true);
    loadPayments(true);
  }, []);

  // Filter tickets for current project
  const projectTickets = tickets.filter(ticket => ticket.projectId === projectId);
  const filteredTickets = projectTickets.filter(ticket => {
    // If no search term, match all tickets
    const matchesSearch = !ticketSearchTerm.trim() || 
                         ticket.title?.toLowerCase().includes(ticketSearchTerm.toLowerCase()) ||
                         ticket.description?.toLowerCase().includes(ticketSearchTerm.toLowerCase());
    const matchesStatus = ticketStatusFilter === 'all' || ticket.status === ticketStatusFilter;
    const matchesPriority = ticketPriorityFilter === 'all' || ticket.priority === ticketPriorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Filter payments for current project - use real-time data from useProjectFinancials
  const filteredPayments = projectPayments.filter(payment => {
    // If no search term, match all payments
    const matchesSearch = !paymentSearchTerm.trim() || 
                         payment.description?.toLowerCase().includes(paymentSearchTerm.toLowerCase()) ||
                         payment.notes?.toLowerCase().includes(paymentSearchTerm.toLowerCase());
    const matchesStatus = paymentStatusFilter === 'all' || payment.status === paymentStatusFilter;
    return matchesSearch && matchesStatus;
  });

  // Project update handlers
  const handleProjectUpdate = async (updates: Partial<Project>) => {
    if (!project || !user) return;

    setIsUpdating(true);
    try {
      await updateProject(project.id, updates);
      await logActivity('project_updated', { projectId: project.id, projectName: project.name });
      toast.success('Project updated successfully');
      setIsEditMode(false);
    } catch (error) {
      console.error('Error updating project:', error);
      toast.error('Failed to update project');
    } finally {
      setIsUpdating(false);
    }
  };

  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
  };

  // Ticket handlers
  const handleCreateTicket = async (data: {
    projectId: string;
    type: TicketType;
    title: string;
    description: string;
    priority: TicketPriority;
    assignedTo: string;
  }) => {
    setTicketCreating(true);
    try {
      const ticketData: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> = {
        projectId: data.projectId,
        title: data.title,
        description: data.description,
        type: data.type,
        priority: data.priority,
        status: 'open' as TicketStatus,
        assignedTo: data.assignedTo,
      };

      await createTicket(ticketData);
      await logActivity('ticket_created', { ticketTitle: data.title, projectId: data.projectId });
      
      toast.success('Ticket created successfully');
      setCreateTicketDialogOpen(false);
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error('Failed to create ticket');
    } finally {
      setTicketCreating(false);
    }
  };

  const handleTicketStatusUpdate = async (ticketId: string, status: TicketStatus) => {
    setTicketUpdating(ticketId);
    try {
      await updateTicket(ticketId, { status });
      await logActivity('ticket_status_change', { ticketId, newStatus: status });
      toast.success('Ticket status updated successfully');
    } catch (error) {
      console.error('Error updating ticket status:', error);
      toast.error('Failed to update ticket status');
    } finally {
      setTicketUpdating(null);
    }
  };

  const handleEditTicket = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setEditTicketDialogOpen(true);
  };

  const handleEditTicketSubmit = async (updates: Partial<Ticket>) => {
    if (!selectedTicket) return;

    try {
      await updateTicket(selectedTicket.id, updates);
      await logActivity('ticket_updated', { ticketId: selectedTicket.id, ticketTitle: selectedTicket.title });
      toast.success('Ticket updated successfully');
      setEditTicketDialogOpen(false);
      setSelectedTicket(null);
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast.error('Failed to update ticket');
    }
  };

  const handleDeleteTicket = async (ticket: Ticket) => {
    try {
      const relationships = await checkTicketRelationships(ticket.id);
      if (!relationships.canDelete) {
        toast.error(`Cannot delete ticket: ${relationships.blockers.join(', ')}`);
        return;
      }

      await deleteTicket(ticket.id);
      await logActivity('ticket_deleted', { ticketId: ticket.id, ticketTitle: ticket.title });
      toast.success('Ticket deleted successfully');
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast.error('Failed to delete ticket');
    }
  };

  // Payment handlers
  const handleCreatePayment = async (data: {
    projectId: string;
    clientId: string;
    amount: number;
    currency: string;
    paymentType: PaymentType;
    description?: string;
    linkedTicketId?: string;
    notes?: string;
  }) => {
    setPaymentCreating(true);
    try {
      // For change request payments, first update the project cost
      if (data.paymentType === 'change_request') {
        const { httpsCallable } = await import('firebase/functions');
        const { functions } = await import('@/lib/firebase');
        const updateProjectCost = httpsCallable(functions, 'updateProjectCostOnChangeRequest');
        
        await updateProjectCost({
          projectId: data.projectId,
          paymentAmount: data.amount,
          paymentType: data.paymentType
        });
      }

      const paymentData: Omit<Payment, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> = {
        projectId: data.projectId,
        clientId: data.clientId,
        amount: data.amount,
        currency: data.currency,
        status: 'pending' as PaymentStatus,
        paymentType: data.paymentType,
        description: data.description,
        linkedTicketId: data.linkedTicketId,
        notes: data.notes,
      };

      await createPayment(paymentData);
      await logActivity('payment_created', { 
        amount: data.amount, 
        projectId: data.projectId,
        paymentType: data.paymentType 
      });
      
      toast.success(`${data.paymentType === 'change_request' ? 'Change request payment' : 'Payment'} created successfully`);
      setCreatePaymentDialogOpen(false);
      refreshFinancials(); // Refresh financial data
    } catch (error) {
      console.error('Error creating payment:', error);
      toast.error('Failed to create payment');
    } finally {
      setPaymentCreating(false);
    }
  };

  const handlePaymentStatusUpdate = async (
    paymentId: string, 
    newStatus: PaymentStatus
  ) => {
    setPaymentUpdating(paymentId);
    try {
      // Only call the Firebase Cloud Function - it handles the payment update and logging
      await updatePaymentStatus(paymentId, newStatus);
      await logActivity('payment_status_change', { paymentId, status: newStatus });
      toast.success(`Payment status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast.error('Failed to update payment status');
    } finally {
      setPaymentUpdating(null);
    }
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setEditPaymentDialogOpen(true);
  };

  const handleEditPaymentSubmit = async (updates: Partial<Payment>) => {
    if (!selectedPayment) return;

    try {
      await updatePayment(selectedPayment.id, updates);
      await logActivity('payment_updated', { paymentId: selectedPayment.id, amount: selectedPayment.amount });
      toast.success('Payment updated successfully');
      setEditPaymentDialogOpen(false);
      setSelectedPayment(null);
      refreshFinancials(); // Refresh financial data
    } catch (error) {
      console.error('Error updating payment:', error);
      toast.error('Failed to update payment');
    }
  };

  const handleDeletePayment = async (payment: Payment) => {
    try {
      await deletePayment(payment.id);
      await logActivity('payment_deleted', { paymentId: payment.id, amount: payment.amount });
      toast.success('Payment deleted successfully');
      refreshFinancials(); // Refresh financial data
    } catch (error) {
      console.error('Error deleting payment:', error);
      toast.error('Failed to delete payment');
    }
  };

  const handleQuickInvoiceGeneration = async (payment: Payment) => {
    if (!payment || payment.status !== 'paid') {
      toast.error('Only paid payments can have invoices generated');
      return;
    }

    if (payment.invoiceId) {
      toast.error('Invoice already exists for this payment');
      return;
    }

    try {
      setPaymentUpdating(payment.id);
      
      const { InvoiceService } = await import('@/services/invoiceService');
      
      const result = await InvoiceService.generateInvoice({
        type: 'individual',
        projectId: payment.projectId,
        clientId: payment.clientId,
        paymentIds: [payment.id],
        description: `Invoice for payment - ${project?.name || 'Project'}`,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      toast.success(`Invoice ${result.invoiceNumber} generated successfully`);
      await logActivity('invoice_generated', { 
        invoiceId: result.invoiceId, 
        paymentId: payment.id,
        invoiceNumber: result.invoiceNumber
      });
      
      // Note: Real-time listener will automatically update the UI with the new invoiceId

    } catch (error) {
      console.error('Error generating invoice:', error);
      toast.error('Failed to generate invoice. Please try again.');
    } finally {
      setPaymentUpdating(null);
    }
  };

  const handleBulkInvoiceGeneration = async () => {
    if (selectedPayments.length === 0) {
      toast.error('Please select payments to generate invoices for');
      return;
    }

    try {
      setBulkInvoiceGenerating(true);
      
      const { InvoiceService } = await import('@/services/invoiceService');
      
      // Get selected payment objects and validate them
      const paymentsToInvoice = filteredPayments.filter(p => 
        selectedPayments.includes(p.id) && canGenerateInvoice(p)
      );

      if (paymentsToInvoice.length === 0) {
        toast.error('No eligible payments selected. Only paid payments without existing invoices can be invoiced.');
        return;
      }

      if (paymentsToInvoice.length !== selectedPayments.length) {
        const skipped = selectedPayments.length - paymentsToInvoice.length;
        toast.warning(`${skipped} payment(s) were skipped (not paid or already have invoices)`);
      }

      // Generate consolidated invoice for multiple payments or individual invoices
      if (paymentsToInvoice.length === 1) {
        const payment = paymentsToInvoice[0];
        const result = await InvoiceService.generateInvoice({
          type: 'individual',
          projectId: payment.projectId,
          clientId: payment.clientId,
          paymentIds: [payment.id],
          description: `Invoice for payment - ${project?.name || 'Project'}`,
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        });
        
        toast.success(`Invoice ${result.invoiceNumber} generated successfully`);
      } else {
        const result = await InvoiceService.generateInvoice({
          type: 'consolidated',
          projectId: projectId,
          clientId: paymentsToInvoice[0].clientId,
          paymentIds: paymentsToInvoice.map(p => p.id),
          description: `Consolidated invoice for ${project?.name || 'Project'}`,
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        });
        
        toast.success(`Consolidated invoice ${result.invoiceNumber} generated for ${paymentsToInvoice.length} payments`);
      }

      await logActivity('invoice_generated', { 
        paymentCount: paymentsToInvoice.length,
        projectId 
      });
      
      setSelectedPayments([]);
      // Note: Real-time listener will automatically update the UI

    } catch (error) {
      console.error('Error generating bulk invoices:', error);
      toast.error('Failed to generate invoices. Please try again.');
    } finally {
      setBulkInvoiceGenerating(false);
    }
  };

  const handleDownloadInvoice = async (payment: Payment) => {
    if (!payment.invoiceId) {
      toast.error('No invoice found for this payment');
      return;
    }

    try {
      setPaymentUpdating(payment.id);
      
      toast.loading('Preparing invoice download...', { id: 'download-invoice' });
      
      // Use the InvoiceService for consistency
      const { InvoiceService } = await import('@/services/invoiceService');
      const result = await InvoiceService.downloadInvoice(payment.invoiceId);
      
      // Check if we got base64 data or a download URL
      if (result.pdfData) {
        // Handle base64 PDF data
        const byteCharacters = atob(result.pdfData);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: result.contentType || 'application/pdf' });
        
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = result.filename || `invoice-${payment.invoiceId}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
      } else if (result.downloadUrl) {
        // Handle download URL (fallback)
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = `invoice-${payment.invoiceId}.pdf`;
        link.target = '_blank'; // Open in new tab as fallback
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error('No download data received');
      }
      
      toast.success('Invoice downloaded successfully', { id: 'download-invoice' });

    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error('Failed to download invoice. Please try again.', { id: 'download-invoice' });
    } finally {
      setPaymentUpdating(null);
    }
  };

  // Bulk download invoices
  const handleBulkDownloadInvoices = async () => {
    const paymentsWithInvoices = filteredPayments.filter(p => 
      selectedPayments.includes(p.id) && canDownloadInvoice(p)
    );

    if (paymentsWithInvoices.length === 0) {
      toast.error('No invoices selected for download');
      return;
    }

    try {
      setBulkInvoiceGenerating(true);
      toast.loading(`Preparing ${paymentsWithInvoices.length} invoice(s) for download...`, { id: 'bulk-download' });

      const { InvoiceService } = await import('@/services/invoiceService');
      
      // Download each invoice
      for (const payment of paymentsWithInvoices) {
        const result = await InvoiceService.downloadInvoice(payment.invoiceId!);
        
        if (!result.downloadUrl) {
          console.error(`No download URL for invoice ${payment.invoiceId}`);
          continue;
        }
        
        // Create download link
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = `invoice-${payment.invoiceId}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Small delay between downloads to avoid overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast.success(`${paymentsWithInvoices.length} invoice(s) downloaded successfully`, { id: 'bulk-download' });
      setSelectedPayments([]);

    } catch (error) {
      console.error('Error downloading invoices:', error);
      toast.error('Failed to download some invoices. Please try again.', { id: 'bulk-download' });
    } finally {
      setBulkInvoiceGenerating(false);
    }
  };

  // Add invoice preview handler
  const handlePreviewInvoice = async (payment: Payment) => {
    if (!payment.invoiceId) {
      toast.error('No invoice found for this payment');
      return;
    }

    try {
      setPaymentUpdating(payment.id);
      
      toast.loading('Preparing invoice preview...', { id: 'preview-invoice' });
      
      const { InvoiceService } = await import('@/services/invoiceService');
      const result = await InvoiceService.downloadInvoice(payment.invoiceId);
      
      // Check if we got base64 data or a download URL
      if (result.pdfData) {
        // Handle base64 PDF data - create blob URL for preview
        const byteCharacters = atob(result.pdfData);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: result.contentType || 'application/pdf' });
        
        // Create blob URL and open in new tab
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
        
        // Clean up the blob URL after a delay
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 60000); // Clean up after 1 minute
        
      } else if (result.downloadUrl) {
        // Handle download URL (fallback)
        window.open(result.downloadUrl, '_blank');
      } else {
        throw new Error('Preview data not received');
      }
      
      toast.success('Invoice opened for preview', { id: 'preview-invoice' });

    } catch (error) {
      console.error('Error previewing invoice:', error);
      toast.error('Failed to preview invoice. Please try again.', { id: 'preview-invoice' });
    } finally {
      setPaymentUpdating(null);
    }
  };

  const togglePaymentSelection = (paymentId: string) => {
    setSelectedPayments(prev => 
      prev.includes(paymentId) 
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
    );
  };

  const selectAllEligiblePayments = () => {
    const eligiblePayments = getEligiblePaymentsForInvoicing();
    setSelectedPayments(eligiblePayments.map(p => p.id));
    if (eligiblePayments.length === 0) {
      toast.info('No eligible payments found. Only paid payments without existing invoices can be selected.');
    } else {
      toast.success(`Selected ${eligiblePayments.length} eligible payment(s) for invoicing`);
    }
  };

  const clearPaymentSelection = () => {
    setSelectedPayments([]);
  };

  const formatCurrency = (amount: number, currency = 'INR') => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        // Handle Firestore Timestamp object
        date = new Date(timestamp.seconds * 1000);
      } else if (typeof timestamp === 'string' || typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else {
        return 'Invalid date';
      }
      
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      return format(date, 'MMM dd, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error, timestamp);
      return 'Invalid date';
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'overdue': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'paid': return 'secondary';
      case 'pending': return 'outline';
      case 'overdue': return 'destructive';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getPriorityIcon = (priority: TicketPriority) => {
    switch (priority) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'low': return <Clock className="h-4 w-4 text-green-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTicketStatusIcon = (status: TicketStatus) => {
    switch (status) {
      case 'open': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'in_progress': return <RefreshCw className="h-4 w-4 text-yellow-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'cancelled': return <CheckCircle className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTicketStatusBadgeVariant = (status: TicketStatus): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'open': return 'outline';
      case 'in_progress': return 'default';
      case 'completed': return 'secondary';
      case 'cancelled': return 'secondary';
      default: return 'outline';
    }
  };

  const getPriorityBadgeVariant = (priority: TicketPriority): "default" | "secondary" | "destructive" | "outline" => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  // Helper functions for payment validation
  const canGenerateInvoice = (payment: Payment) => {
    return payment.status === 'paid' && !payment.invoiceId;
  };

  const canDownloadInvoice = (payment: Payment) => {
    return payment.status === 'paid' && !!payment.invoiceId;
  };

  const getEligiblePaymentsForInvoicing = () => {
    return filteredPayments.filter(canGenerateInvoice);
  };

  // Helper function to get payments with downloadable invoices
  const getPaymentsWithInvoices = () => {
    return filteredPayments.filter(p => canDownloadInvoice(p));
  };

  // Helper function to check if selected payments have downloadable invoices
  const getSelectedPaymentsWithInvoices = () => {
    return filteredPayments.filter(p => 
      selectedPayments.includes(p.id) && canDownloadInvoice(p)
    );
  };

  if (loading && !project) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold">Project not found</h2>
        <Link href="/admin/projects">
          <Button variant="outline" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/admin/projects">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">{project.name}</h1>
              <p className="text-muted-foreground">{project.description}</p>
            </div>
            <Badge variant={getStatusBadgeVariant(project.status)}>
              {project.status}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            {isEditMode ? (
              <>
                <Button 
                  onClick={cancelEdit}
                  variant="outline"
                  size="sm"
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
              </>
            ) : (
              <>
                <Button 
                  onClick={toggleEditMode}
                  variant="outline"
                  size="sm"
                >
                  <User className="h-4 w-4 mr-2" />
                  Edit Project
                </Button>
                <Button 
                  onClick={refreshFinancials} 
                  disabled={calculating}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${calculating ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Financial Summary Cards */}
        {financialSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalCost, project.currency)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total project cost
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalPaid, project.currency)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {financialSummary.paymentProgress.toFixed(1)}% of total cost
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(financialSummary.totalPending, project.currency)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {financialSummary.paymentsCount} total payments
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tickets</CardTitle>
                <TicketIcon className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {projectTickets.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {projectTickets.filter(t => t.status === 'open').length} open tickets
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tickets">Tickets ({projectTickets.length})</TabsTrigger>
            <TabsTrigger value="payments">Payments & Invoices ({projectPayments.length})</TabsTrigger>
            <TabsTrigger value="financials">Financial Details</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="space-y-6">
              {/* Project Information and Clients Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <InlineProjectEdit
                  project={project}
                  isEditMode={isEditMode}
                  onSave={handleProjectUpdate}
                  onCancel={cancelEdit}
                  isLoading={isUpdating}
                />
                
                <ProjectClientsManager projectId={projectId} />
              </div>

              {/* Financial Summary - now the single source of truth */}
              {financialSummary && (
                <Card>
                  <CardHeader>
                    <CardTitle>Financial Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Total Cost</label>
                        <p className="text-lg font-semibold">{formatCurrency(financialSummary.totalCost, project.currency)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Total Paid</label>
                        <p className="text-lg font-semibold">{formatCurrency(financialSummary.totalPaid, project.currency)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Payment Progress</label>
                        <p className="text-sm text-muted-foreground">{financialSummary.paymentProgress.toFixed(1)}% of total cost</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Last Updated</label>
                        <p className="text-sm text-muted-foreground">{formatDate(financialSummary.lastCalculatedAt)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="tickets">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <TicketIcon className="h-5 w-5" />
                    Project Tickets
                  </CardTitle>
                  <Dialog open={createTicketDialogOpen} onOpenChange={setCreateTicketDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Ticket
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Create New Ticket</DialogTitle>
                        <DialogDescription>
                          Create a new support ticket for this project.
                        </DialogDescription>
                      </DialogHeader>
                      <TicketCreationForm
                        onSubmit={handleCreateTicket}
                        onCancel={() => setCreateTicketDialogOpen(false)}
                        isLoading={ticketCreating}
                        preselectedProjectId={projectId}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
                
                {/* Ticket Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mt-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Search tickets..."
                        value={ticketSearchTerm}
                        onChange={(e) => setTicketSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={ticketStatusFilter} onValueChange={setTicketStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={ticketPriorityFilter} onValueChange={setTicketPriorityFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="All Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                {filteredTickets.length === 0 ? (
                  <div className="text-center py-8">
                    <TicketIcon className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No tickets found</h3>
                    <p className="text-muted-foreground mb-4">
                      {projectTickets.length === 0 
                        ? "No tickets have been created for this project yet."
                        : "No tickets match your current filters."
                      }
                    </p>
                    <Button onClick={() => setCreateTicketDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Ticket
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Assigned To</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTickets.map((ticket) => (
                        <TableRow key={ticket.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{ticket.title}</div>
                              <div className="text-sm text-muted-foreground line-clamp-1">
                                {ticket.description}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {ticket.type.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {getPriorityIcon(ticket.priority)}
                              <Badge variant={getPriorityBadgeVariant(ticket.priority)} className="capitalize">
                                {ticket.priority}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getTicketStatusIcon(ticket.status)}
                              <Select
                                value={ticket.status}
                                onValueChange={(value: TicketStatus) => handleTicketStatusUpdate(ticket.id, value)}
                                disabled={ticketUpdating === ticket.id}
                              >
                                <SelectTrigger className="w-[130px] h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="open">Open</SelectItem>
                                  <SelectItem value="in_progress">In Progress</SelectItem>
                                  <SelectItem value="completed">Completed</SelectItem>
                                  <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span className="text-sm">{ticket.assignedToName || 'Unassigned'}</span>
                            </div>
                          </TableCell>
                          <TableCell>{formatDate(ticket.createdAt)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditTicket(ticket)}
                              >
                                Edit
                              </Button>
                              <DeleteConfirmationDialog
                                title="Delete Ticket"
                                description="Are you sure you want to delete this ticket? This action cannot be undone."
                                itemName={ticket.title}
                                onConfirm={() => handleDeleteTicket(ticket)}
                              >
                                <Button variant="outline" size="sm">
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </DeleteConfirmationDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Payments & Invoices
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {selectedPayments.length > 0 && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearPaymentSelection}
                        >
                          Clear Selection
                        </Button>
                        {getSelectedPaymentsWithInvoices().length > 0 && (
                          <Button
                            size="sm"
                            onClick={handleBulkDownloadInvoices}
                            disabled={bulkInvoiceGenerating}
                            variant="outline"
                            className="gap-2"
                          >
                            {bulkInvoiceGenerating ? (
                              <>
                                <RefreshCw className="h-4 w-4 animate-spin" />
                                Downloading...
                              </>
                            ) : (
                              <>
                                <Download className="h-4 w-4" />
                                Download {getSelectedPaymentsWithInvoices().length} Invoice{getSelectedPaymentsWithInvoices().length > 1 ? 's' : ''}
                              </>
                            )}
                          </Button>
                        )}
                        {filteredPayments.filter(p => selectedPayments.includes(p.id) && canGenerateInvoice(p)).length > 0 && (
                          <Button
                            size="sm"
                            onClick={handleBulkInvoiceGeneration}
                            disabled={bulkInvoiceGenerating}
                            className="gap-2"
                          >
                            {bulkInvoiceGenerating ? (
                              <>
                                <RefreshCw className="h-4 w-4 animate-spin" />
                                Generating...
                              </>
                            ) : (
                              <>
                                <FileText className="h-4 w-4" />
                                Generate {filteredPayments.filter(p => selectedPayments.includes(p.id) && canGenerateInvoice(p)).length} Invoice{filteredPayments.filter(p => selectedPayments.includes(p.id) && canGenerateInvoice(p)).length > 1 ? 's' : ''}
                              </>
                            )}
                          </Button>
                        )}
                      </>
                    )}
                    <Dialog open={createPaymentDialogOpen} onOpenChange={setCreatePaymentDialogOpen}>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Payment
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Create New Payment</DialogTitle>
                          <DialogDescription>
                            Create a new payment request for this project.
                          </DialogDescription>
                        </DialogHeader>
                        <PaymentCreationForm
                          onSubmit={handleCreatePayment}
                          onCancel={() => setCreatePaymentDialogOpen(false)}
                          isLoading={paymentCreating}
                          preselectedProjectId={projectId}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                {/* Payment Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mt-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Search payments..."
                        value={paymentSearchTerm}
                        onChange={(e) => setPaymentSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  {getEligiblePaymentsForInvoicing().length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectAllEligiblePayments}
                      className="gap-2"
                    >
                      <FileText className="h-4 w-4" />
                      Select All Eligible
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {/* Information Section */}
                {(getEligiblePaymentsForInvoicing().length > 0 || getPaymentsWithInvoices().length > 0) && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                      <div className="text-sm text-blue-800">
                        <strong>Invoice Management:</strong> 
                        {getEligiblePaymentsForInvoicing().length > 0 && (
                          <span> Generate invoices for {getEligiblePaymentsForInvoicing().length} paid payment{getEligiblePaymentsForInvoicing().length > 1 ? 's' : ''}.</span>
                        )}
                        {getPaymentsWithInvoices().length > 0 && (
                          <span> {getEligiblePaymentsForInvoicing().length > 0 ? ' ' : ''}Preview or download {getPaymentsWithInvoices().length} existing invoice{getPaymentsWithInvoices().length > 1 ? 's' : ''}.</span>
                        )}
                        {' '}Select multiple items for bulk operations.
                      </div>
                    </div>
                  </div>
                )}

                {filteredPayments.length === 0 ? (
                  <div className="text-center py-8">
                    <CreditCard className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No payments found</h3>
                    <p className="text-muted-foreground mb-4">
                      {projectPayments.length === 0 
                        ? "No payments have been created for this project yet."
                        : "No payments match your current filters."
                      }
                    </p>
                    <Button onClick={() => setCreatePaymentDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Payment
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredPayments.map((payment) => (
                      <div key={payment.id} className="flex items-center gap-4 p-4 border rounded-lg">
                        {/* Selection Checkbox - show for payments that can have invoices generated OR downloaded */}
                        {(canGenerateInvoice(payment) || canDownloadInvoice(payment)) && (
                          <Checkbox
                            checked={selectedPayments.includes(payment.id)}
                            onCheckedChange={() => togglePaymentSelection(payment.id)}
                            aria-label={`Select payment of ${formatCurrency(payment.amount, payment.currency)} for ${canGenerateInvoice(payment) ? 'invoice generation' : 'invoice download'}`}
                          />
                        )}
                        
                        <div className="flex items-center justify-between flex-1">
                          <div className="flex items-center gap-4">
                            {getPaymentStatusIcon(payment.status)}
                            <div>
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium">
                                  {formatCurrency(payment.amount, payment.currency)}
                                </span>
                                {payment.paymentType === 'change_request' && (
                                  <Badge variant="outline" className="text-orange-600 border-orange-300">
                                    Change Request
                                  </Badge>
                                )}
                                {payment.paymentType === 'regular' && (
                                  <Badge variant="outline" className="text-blue-600 border-blue-300">
                                    Regular
                                  </Badge>
                                )}
                                {payment.invoiceId && (
                                  <Badge variant="outline" className="text-green-600 border-green-300">
                                    <FileText className="h-3 w-3 mr-1" />
                                    Invoiced
                                  </Badge>
                                )}
                                {!canGenerateInvoice(payment) && !canDownloadInvoice(payment) && payment.status !== 'paid' && (
                                  <Badge variant="outline" className="text-gray-500 border-gray-300">
                                    {payment.status === 'pending' ? 'Payment Pending' : 
                                     payment.status === 'overdue' ? 'Payment Overdue' : 
                                     payment.status === 'cancelled' ? 'Payment Cancelled' : 'Not Eligible'}
                                  </Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Created {formatDate(payment.createdAt)}
                                {payment.paidAt && ` • Paid ${formatDate(payment.paidAt)}`}
                              </div>
                              {payment.description && (
                                <div className="text-sm text-muted-foreground mt-1">
                                  <strong>Description:</strong> {payment.description}
                                </div>
                              )}
                              {payment.notes && (
                                <div className="text-sm text-muted-foreground mt-1">
                                  <strong>Notes:</strong> {payment.notes}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant={getStatusBadgeVariant(payment.status)}>
                              {payment.status}
                            </Badge>
                            
                            {payment.status !== 'paid' && (
                              <Button
                                size="sm"
                                onClick={() => handlePaymentStatusUpdate(payment.id, 'paid')}
                                disabled={paymentUpdating === payment.id}
                              >
                                Mark Paid
                              </Button>
                            )}
                            
                            {canGenerateInvoice(payment) && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickInvoiceGeneration(payment)}
                                disabled={paymentUpdating === payment.id}
                                className="gap-2"
                              >
                                {paymentUpdating === payment.id ? (
                                  <>
                                    <RefreshCw className="h-3 w-3 animate-spin" />
                                    Generating...
                                  </>
                                ) : (
                                  <>
                                    <FileText className="h-3 w-3" />
                                    Generate Invoice
                                  </>
                                )}
                              </Button>
                            )}
                            
                            {canDownloadInvoice(payment) && (
                              <div className="flex items-center gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handlePreviewInvoice(payment)}
                                  disabled={paymentUpdating === payment.id}
                                  className="gap-2"
                                >
                                  {paymentUpdating === payment.id ? (
                                    <>
                                      <RefreshCw className="h-3 w-3 animate-spin" />
                                      Loading...
                                    </>
                                  ) : (
                                    <>
                                      <Eye className="h-3 w-3" />
                                      Preview
                                    </>
                                  )}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownloadInvoice(payment)}
                                  disabled={paymentUpdating === payment.id}
                                  className="gap-2"
                                >
                                  {paymentUpdating === payment.id ? (
                                    <>
                                      <RefreshCw className="h-3 w-3 animate-spin" />
                                      Downloading...
                                    </>
                                  ) : (
                                    <>
                                      <Download className="h-3 w-3" />
                                      Download
                                    </>
                                  )}
                                </Button>
                              </div>
                            )}
                            
                            {payment.status === 'paid' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePaymentStatusUpdate(payment.id, 'pending')}
                                disabled={paymentUpdating === payment.id}
                              >
                                Mark Pending
                              </Button>
                            )}

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditPayment(payment)}
                            >
                              Edit
                            </Button>

                            <DeleteConfirmationDialog
                              title="Delete Payment"
                              description="Are you sure you want to delete this payment? This action cannot be undone."
                              itemName={`${formatCurrency(payment.amount, payment.currency)} payment`}
                              onConfirm={() => handleDeletePayment(payment)}
                            >
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </DeleteConfirmationDialog>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financials">
            {financialSummary ? (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Payment Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm">Total Paid:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(financialSummary.totalPaid, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Pending:</span>
                        <span className="font-medium text-yellow-600">
                          {formatCurrency(financialSummary.totalPending, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Overdue:</span>
                        <span className="font-medium text-red-600">
                          {formatCurrency(financialSummary.totalOverdue, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cancelled:</span>
                        <span className="font-medium text-gray-600">
                          {formatCurrency(financialSummary.totalCancelled, project.currency)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Financial Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm">Total Cost:</span>
                        <span className="font-medium">
                          {formatCurrency(financialSummary.totalCost, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Total Paid:</span>
                        <span className="font-medium">
                          {formatCurrency(financialSummary.totalPaid, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Payment Progress:</span>
                        <span className="font-medium">
                          {financialSummary.paymentProgress.toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Outstanding:</span>
                        <span className="font-medium">
                          {formatCurrency(financialSummary.totalCost - financialSummary.totalPaid, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Total Payments:</span>
                        <span className="font-medium">
                          {financialSummary.paymentsCount}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Change Request Breakdown Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Payment Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm">Base Project Cost:</span>
                        <span className="font-medium text-blue-600">
                          {formatCurrency(financialSummary.baseProjectCost, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Regular Payments:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(financialSummary.regularPayments, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Change Request Payments:</span>
                        <span className="font-medium text-orange-600">
                          {formatCurrency(financialSummary.changeRequestPayments, project.currency)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Total Change Request Cost:</span>
                        <span className="font-medium text-orange-600">
                          {formatCurrency(financialSummary.totalChangeRequestCost, project.currency)}
                        </span>
                      </div>
                      {financialSummary.totalChangeRequestCost > 0 && (
                        <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-md dark:bg-orange-950 dark:border-orange-800">
                          <p className="text-xs text-orange-700 dark:text-orange-400">
                            Project cost increased by {formatCurrency(financialSummary.totalChangeRequestCost, project.currency)} due to change requests
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="py-8">
                  <div className="text-center">
                    <RefreshCw className={`mx-auto h-8 w-8 mb-4 ${loading ? 'animate-spin' : ''}`} />
                    <p className="text-muted-foreground">Loading financial data...</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>


        </Tabs>

        {/* Edit Ticket Dialog */}
        <Dialog open={editTicketDialogOpen} onOpenChange={setEditTicketDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Ticket</DialogTitle>
              <DialogDescription>
                Modify the details of this support ticket.
              </DialogDescription>
            </DialogHeader>
            {selectedTicket && (
              <TicketEditForm
                ticket={selectedTicket}
                onSubmit={handleEditTicketSubmit}
                onCancel={() => setEditTicketDialogOpen(false)}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Payment Dialog */}
        <Dialog open={editPaymentDialogOpen} onOpenChange={setEditPaymentDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Payment</DialogTitle>
              <DialogDescription>
                Modify the details of this payment request.
              </DialogDescription>
            </DialogHeader>
            {selectedPayment && (
              <PaymentEditForm
                payment={selectedPayment}
                onSubmit={handleEditPaymentSubmit}
                onCancel={() => setEditPaymentDialogOpen(false)}
              />
            )}
          </DialogContent>
        </Dialog>
    </div>
  );
}

export function ProjectDetailClient({ projectId }: ProjectDetailClientProps) {
  return (
    <ProjectManagementProvider>
      <UserManagementProvider>
        <AdminLayout>
          <ProjectDetailContent projectId={projectId} />
        </AdminLayout>
      </UserManagementProvider>
    </ProjectManagementProvider>
  );
}