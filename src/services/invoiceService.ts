import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase';
import { InvoiceFilters, InvoiceListResponse, Invoice } from '@/types/invoice';

export class InvoiceService {
  // Generate individual invoice
  static async generateInvoice(data: {
    type: 'individual' | 'consolidated';
    projectId: string;
    clientId: string;
    paymentIds: string[];
    description?: string;
    notes?: string;
    dueDate?: Date;
  }): Promise<{
    success: boolean;
    invoiceId: string;
    invoiceNumber: string;
    totalAmount: number;
    paymentCount: number;
  }> {
    const generateInvoice = httpsCallable(functions, 'generateInvoice');
    const result = await generateInvoice(data);
    return result.data as any;
  }

  // Generate consolidated invoice
  static async generateConsolidatedInvoice(data: {
    projectId: string;
    clientId: string;
    paymentIds: string[];
    description?: string;
    notes?: string;
    dueDate?: Date;
  }): Promise<{
    success: boolean;
    invoiceId: string;
    invoiceNumber: string;
    totalAmount: number;
    paymentCount: number;
  }> {
    const generateConsolidatedInvoice = httpsCallable(functions, 'generateConsolidatedInvoice');
    const result = await generateConsolidatedInvoice(data);
    return result.data as any;
  }

  // Get invoices with filtering
  static async getInvoices(filters: InvoiceFilters = {}, limit = 50): Promise<InvoiceListResponse> {
    const getInvoices = httpsCallable(functions, 'getInvoices');
    const result = await getInvoices({ filters, limit });
    return result.data as InvoiceListResponse;
  }

  // Update invoice status
  static async updateInvoiceStatus(
    invoiceId: string,
    newStatus: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled',
    reason?: string
  ): Promise<{
    success: boolean;
    invoiceId: string;
    oldStatus: string;
    newStatus: string;
  }> {
    const updateInvoiceStatus = httpsCallable(functions, 'updateInvoiceStatus');
    const result = await updateInvoiceStatus({ invoiceId, newStatus, reason });
    return result.data as any;
  }

  // Download invoice PDF
  static async downloadInvoice(invoiceId: string): Promise<{
    success: boolean;
    pdfData?: string;
    filename?: string;
    contentType?: string;
    downloadUrl?: string;
  }> {
    const downloadInvoice = httpsCallable(functions, 'downloadInvoice');
    const result = await downloadInvoice({ invoiceId });
    return result.data as { 
      success: boolean; 
      pdfData?: string;
      filename?: string;
      contentType?: string;
      downloadUrl?: string;
    };
  }

  // Helper: Format currency
  static formatCurrency(amount: number, currency = 'INR'): string {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  }

  // Helper: Get status badge variant
  static getStatusBadgeVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
    switch (status) {
      case 'paid': return 'secondary';
      case 'sent': return 'default';
      case 'draft': return 'outline';
      case 'overdue': return 'destructive';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  }

  // Helper: Get invoice type badge variant
  static getTypeBadgeVariant(type: string): "default" | "secondary" {
    switch (type) {
      case 'individual': return 'default';
      case 'consolidated': return 'secondary';
      default: return 'outline' as any;
    }
  }
}
