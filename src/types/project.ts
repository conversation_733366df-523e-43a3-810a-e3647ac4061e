import { Timestamp } from 'firebase/firestore';

// Simplified Project Management Types
export type ProjectStatus = 'draft' | 'active' | 'completed' | 'cancelled';
export type TicketType = 'development' | 'maintenance' | 'support';
export type TicketStatus = 'open' | 'in_progress' | 'completed' | 'cancelled';
export type TicketPriority = 'low' | 'medium' | 'high';
export type PaymentStatus = 'pending' | 'paid' | 'overdue' | 'cancelled';
export type PaymentType = 'regular' | 'change_request';

// Simplified Project Model - Financial data moved to ProjectFinancialSummary
export interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  totalCost: number; // Total project cost including base + approved changes
  currency: string;
  startDate: Timestamp;
  endDate?: Timestamp;
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Simplified ProjectClient Model
export interface ProjectClient {
  id: string;
  projectId: string;
  clientId: string;
  addedAt: Timestamp;
  addedBy: string;
  isPrimary?: boolean;
}

// Simplified Ticket Model
export interface Ticket {
  id: string;
  projectId: string;
  title: string;
  description: string;
  type: TicketType;
  priority: TicketPriority;
  status: TicketStatus;
  assignedTo: string; // Single assignee
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  completedAt?: Timestamp;
}

// Enhanced Payment Model
export interface Payment {
  id: string;
  projectId: string;
  clientId: string;
  amount: number; // For regular: payment amount. For change_request: payment amount that also increases project cost
  currency: string;
  status: PaymentStatus;
  paymentType: PaymentType; // Type of payment
  description?: string; // Better description field
  paidAt?: Timestamp;
  linkedTicketId?: string; // Optional reference to specific ticket
  notes?: string;
  // Invoice tracking fields
  invoiceId?: string; // Reference to generated invoice
  invoicedAt?: Timestamp; // When invoice was generated
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// List/Response Types
export interface ProjectListItem extends Project {
  clientCount: number;
  ticketCount: number;
  createdByName?: string;
}

export interface TicketListItem extends Ticket {
  projectName?: string;
  createdByName?: string;
  assignedToName?: string;
}

export interface PaymentListItem extends Payment {
  projectName?: string;
  clientName?: string;
  createdByName?: string;
}

// Filter Types
export interface ProjectFilters {
  status?: ProjectStatus;
  createdBy?: string;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TicketFilters {
  projectId?: string;
  type?: TicketType;
  status?: TicketStatus;
  priority?: TicketPriority;
  assignedTo?: string;
  createdBy?: string;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaymentFilters {
  projectId?: string;
  clientId?: string;
  status?: PaymentStatus;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Response Types
export interface ProjectListResponse {
  projects: ProjectListItem[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export interface TicketListResponse {
  tickets: TicketListItem[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export interface PaymentListResponse {
  payments: PaymentListItem[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// Relationship Check Types
export interface ProjectRelationshipCheck {
  canDelete: boolean;
  blockers: string[];
  ticketCount: number;
  paymentCount: number;
}

export interface TicketRelationshipCheck {
  canDelete: boolean;
  blockers: string[];
  paymentCount: number;
}

// Context Types
export interface ProjectManagementContextType {
  // Projects
  projects: ProjectListItem[];
  projectsLoading: boolean;
  projectsTotal: number;
  projectsHasMore: boolean;
  projectFilters: ProjectFilters;
  
  // Tickets
  tickets: TicketListItem[];
  ticketsLoading: boolean;
  ticketsTotal: number;
  ticketsHasMore: boolean;
  ticketFilters: TicketFilters;
  
  // Payments
  payments: PaymentListItem[];
  paymentsLoading: boolean;
  paymentsTotal: number;
  paymentsHasMore: boolean;
  paymentFilters: PaymentFilters;
  
  // Actions
  setProjectFilters: (filters: ProjectFilters) => void;
  setTicketFilters: (filters: TicketFilters) => void;
  setPaymentFilters: (filters: PaymentFilters) => void;
  loadProjects: (reset?: boolean) => Promise<void>;
  loadTickets: (reset?: boolean) => Promise<void>;
  loadPayments: (reset?: boolean) => Promise<void>;
  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>) => Promise<void>;
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  checkProjectRelationships: (id: string) => Promise<ProjectRelationshipCheck>;
  createTicket: (ticket: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>) => Promise<void>;
  updateTicket: (id: string, updates: Partial<Ticket>) => Promise<void>;
  deleteTicket: (id: string) => Promise<void>;
  checkTicketRelationships: (id: string) => Promise<TicketRelationshipCheck>;
  createPayment: (payment: Omit<Payment, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>) => Promise<void>;
  updatePayment: (id: string, updates: Partial<Payment>) => Promise<void>;
  deletePayment: (id: string) => Promise<void>;
  refreshAll: () => Promise<void>;
}

// Input Types for Forms
export interface ProjectInput {
  name: string;
  description: string;
  status: ProjectStatus;
  totalCost: number;
  currency: string;
  startDate: Date;
  endDate?: Date;
}

export interface TicketInput {
  projectId: string;
  title: string;
  description: string;
  type: TicketType;
  priority: TicketPriority;
  assignedTo: string;
}

export interface PaymentInput {
  projectId: string;
  clientId: string;
  amount: number;
  currency: string;
  paymentType: PaymentType;
  description?: string;
  linkedTicketId?: string;
  notes?: string;
}

// Enhanced Financial Summary Interface - Single source of truth for all financial data
export interface ProjectFinancialSummary {
  projectId: string;
  totalCost: number; // Total project cost (base + approved changes)
  totalPaid: number; // Total amount actually paid by client
  
  // Payment breakdowns by type
  regularPayments: number;
  changeRequestPayments: number;
  
  // Payment status totals
  totalPending: number;
  totalOverdue: number;
  totalCancelled: number;
  
  // Progress tracking
  paymentProgress: number; // (totalPaid / totalCost) * 100
  
  // Change request tracking
  baseProjectCost: number; // Original project cost
  totalChangeRequestCost: number; // Total from change requests
  
  // Metadata
  paymentsCount: number;
  lastPaymentDate?: Timestamp;
  lastCalculatedAt: Timestamp;
  autoCalculated: boolean; // Always true now
}

// Removed PaymentCostUpdate and ProjectCostLog interfaces - no longer needed 